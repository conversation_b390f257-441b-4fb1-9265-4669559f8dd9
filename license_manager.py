#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理模块 - 处理软件试用和激活验证
"""

import hashlib
import platform
import subprocess
import uuid
import requests
import json
import os
from datetime import datetime
import time

class LicenseManager:
    """许可证管理器"""
    
    def __init__(self):
        try:
            from config import CLOUD_FUNCTION_URL, REQUEST_TIMEOUT
            self.api_url = CLOUD_FUNCTION_URL
            self.timeout = REQUEST_TIMEOUT
        except ImportError:
            # 如果配置文件不存在，使用默认值
            self.api_url = "https://service-xxx.gz.apigw.tencentcs.com/release"
            self.timeout = 10

        self.device_id = self.get_device_fingerprint()
        self.local_cache_file = "license_cache.dat"
    
    def get_device_fingerprint(self):
        """生成设备唯一指纹"""
        fingerprint_data = []
        
        try:
            if platform.system() == "Windows":
                # CPU信息
                try:
                    cpu_info = subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode()
                    fingerprint_data.append(cpu_info.strip())
                except:
                    pass
                
                # 主板序列号
                try:
                    motherboard = subprocess.check_output("wmic baseboard get serialnumber", shell=True).decode()
                    fingerprint_data.append(motherboard.strip())
                except:
                    pass
            
            # MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff) for i in range(0,8*6,8)][::-1])
            fingerprint_data.append(mac)
            
        except:
            pass
        
        # 备用方案
        if not fingerprint_data:
            fingerprint_data.append(platform.node())
            fingerprint_data.append(str(uuid.getnode()))
            fingerprint_data.append(platform.system())
        
        # 生成最终指纹
        combined = ''.join(fingerprint_data)
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def check_license_status(self):
        """检查许可证状态"""
        try:
            data = {"device_id": self.device_id}
            
            response = requests.post(
                f"{self.api_url}/check",
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                self.save_local_cache(result)
                return True, result
            else:
                # 网络错误，使用本地缓存
                cached_result = self.load_local_cache()
                return False, cached_result
                
        except Exception as e:
            print(f"检查许可证状态失败: {e}")
            cached_result = self.load_local_cache()
            return False, cached_result
    
    def record_software_usage(self):
        """记录软件使用"""
        try:
            data = {"device_id": self.device_id}
            
            response = requests.post(
                f"{self.api_url}/use",
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.save_local_cache(result)
                return True, result
            else:
                return False, {"success": False, "error": "网络错误"}
                
        except Exception as e:
            return False, {"success": False, "error": str(e)}
    
    def activate_software(self, activation_code):
        """激活软件"""
        try:
            data = {
                "device_id": self.device_id,
                "activation_code": activation_code.strip().upper()
            }
            
            response = requests.post(
                f"{self.api_url}/activate",
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    # 激活成功，保存到本地缓存
                    cache_data = {
                        "is_activated": True,
                        "activation_code": activation_code,
                        "can_use": True
                    }
                    self.save_local_cache(cache_data)
                return True, result
            else:
                return False, {"success": False, "message": "网络错误"}
                
        except Exception as e:
            return False, {"success": False, "message": str(e)}
    
    def save_local_cache(self, data):
        """保存本地缓存"""
        try:
            cache_data = {
                "device_id": self.device_id,
                "data": data,
                "timestamp": time.time()
            }
            
            # 简单加密存储
            cache_json = json.dumps(cache_data)
            encrypted_data = self.simple_encrypt(cache_json)
            
            with open(self.local_cache_file, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def load_local_cache(self):
        """加载本地缓存"""
        try:
            if os.path.exists(self.local_cache_file):
                with open(self.local_cache_file, 'rb') as f:
                    encrypted_data = f.read()
                
                # 解密
                cache_json = self.simple_decrypt(encrypted_data)
                cache_data = json.loads(cache_json)
                
                # 验证设备ID
                if cache_data.get("device_id") == self.device_id:
                    # 检查缓存是否过期（7天）
                    cache_time = cache_data.get("timestamp", 0)
                    if time.time() - cache_time < 7 * 24 * 3600:
                        return cache_data.get("data", {})
        except Exception as e:
            print(f"加载缓存失败: {e}")
        
        return {"can_use": False, "error": "无法验证许可证", "is_activated": False}
    
    def simple_encrypt(self, data):
        """简单加密"""
        key = hashlib.md5(self.device_id.encode()).digest()
        encrypted = bytearray()
        
        for i, byte in enumerate(data.encode('utf-8')):
            encrypted.append(byte ^ key[i % len(key)])
        
        return bytes(encrypted)
    
    def simple_decrypt(self, encrypted_data):
        """简单解密"""
        key = hashlib.md5(self.device_id.encode()).digest()
        decrypted = bytearray()
        
        for i, byte in enumerate(encrypted_data):
            decrypted.append(byte ^ key[i % len(key)])
        
        return decrypted.decode('utf-8')
    
    def can_use_software(self):
        """检查是否可以使用软件"""
        online, status = self.check_license_status()
        
        # 如果在线检查成功，使用在线结果
        if online:
            return status.get("can_use", False), status
        
        # 离线模式，使用缓存
        if status.get("is_activated"):
            return True, status
        
        # 未激活且无法在线验证
        return False, status
    
    def get_status_message(self, status):
        """获取状态消息"""
        if status.get("error"):
            return f"验证失败: {status['error']}"
        
        if status.get("is_activated"):
            return "软件已激活"
        
        remaining = status.get("remaining_trials", 0)
        if remaining > 0:
            return f"试用版本，剩余 {remaining} 次使用机会"
        else:
            return "试用次数已用完，请激活软件"
    
    def is_first_run(self):
        """检查是否首次运行"""
        return not os.path.exists(self.local_cache_file)
