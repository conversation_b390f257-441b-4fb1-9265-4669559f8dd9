#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发布版本构建脚本 - 打包用户端exe文件
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

class ReleaseBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.release_dir = self.project_root / "release"
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        
        # 用户端文件列表（需要打包的文件）
        self.user_files = [
            "main.py",
            "gui_module.py", 
            "license_manager.py",
            "config.py",  # 用户端配置（不含敏感信息）
            "rename_engine.py",
            "file_reader.py",
            "pattern_generator.py",
            "requirements.txt",
            "使用说明.md"
        ]
        
        # 管理员文件列表（不打包的文件）
        self.admin_files = [
            "activation_code_manager.py",  # 激活码管理工具
            "cloud_function.py",          # 云函数代码
            "admin_config.py",            # 管理员配置
            "build_release.py",           # 构建脚本
            "deployment_guide.md"         # 部署指南
        ]
    
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        for dir_path in [self.build_dir, self.dist_dir, self.release_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
            dir_path.mkdir(exist_ok=True)
        
        print("✅ 构建目录清理完成")
    
    def copy_user_files(self):
        """复制用户端文件到发布目录"""
        print("📁 复制用户端文件...")
        
        src_dir = self.release_dir / "src"
        src_dir.mkdir(exist_ok=True)
        
        copied_count = 0
        for file_name in self.user_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                dst_file = src_dir / file_name
                shutil.copy2(src_file, dst_file)
                copied_count += 1
                print(f"  ✅ {file_name}")
            else:
                print(f"  ⚠️ 文件不存在: {file_name}")
        
        print(f"✅ 复制完成，共 {copied_count} 个文件")
    
    def verify_no_admin_files(self):
        """验证没有管理员文件被意外包含"""
        print("🔍 验证文件安全性...")
        
        src_dir = self.release_dir / "src"
        issues = []
        
        for file_name in self.admin_files:
            if (src_dir / file_name).exists():
                issues.append(file_name)
        
        if issues:
            print("❌ 发现管理员文件被意外包含:")
            for file_name in issues:
                print(f"  - {file_name}")
            return False
        
        print("✅ 文件安全性验证通过")
        return True
    
    def check_config_security(self):
        """检查配置文件安全性"""
        print("🔒 检查配置文件安全性...")
        
        config_file = self.release_dir / "src" / "config.py"
        if config_file.exists():
            content = config_file.read_text(encoding='utf-8')
            
            # 检查是否包含敏感信息
            sensitive_keywords = [
                "ADMIN_SECRET_KEY",
                "admin_key",
                "password",
                "secret"
            ]
            
            issues = []
            for keyword in sensitive_keywords:
                if keyword in content and not content.count(f"# {keyword}"):
                    issues.append(keyword)
            
            if issues:
                print("❌ 配置文件包含敏感信息:")
                for keyword in issues:
                    print(f"  - {keyword}")
                return False
        
        print("✅ 配置文件安全性检查通过")
        return True
    
    def build_exe(self):
        """构建exe文件"""
        print("🔨 开始构建exe文件...")
        
        src_dir = self.release_dir / "src"
        os.chdir(src_dir)
        
        # PyInstaller命令
        cmd = [
            "pyinstaller",
            "--onefile",                    # 单文件模式
            "--noconsole",                  # 无控制台窗口
            "--name", "批量重命名工具",      # exe文件名
            "--add-data", "config.py;.",    # 包含配置文件
            "main.py"                       # 主程序
        ]

        # 如果图标文件存在，添加图标参数
        # 在项目根目录查找图标文件
        icon_file = self.project_root / "icon.ico"
        if icon_file.exists():
            # 复制图标文件到构建目录
            local_icon = Path("icon.ico")
            shutil.copy2(icon_file, local_icon)
            cmd.extend(["--icon", "icon.ico"])
            print("  📎 使用自定义图标")
        else:
            print("  ℹ️ 未找到图标文件，使用默认图标")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ exe构建成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ exe构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
        except FileNotFoundError:
            print("❌ PyInstaller未安装，请运行: pip install pyinstaller")
            return False
    
    def create_release_package(self):
        """创建发布包"""
        print("📦 创建发布包...")
        
        # 复制exe文件
        src_exe = self.release_dir / "src" / "dist" / "批量重命名工具.exe"
        if src_exe.exists():
            dst_exe = self.release_dir / "批量重命名工具.exe"
            shutil.copy2(src_exe, dst_exe)
            
            # 获取文件大小
            file_size = src_exe.stat().st_size / (1024 * 1024)
            print(f"✅ exe文件已创建: {dst_exe}")
            print(f"📊 文件大小: {file_size:.2f} MB")
        else:
            print("❌ 未找到构建的exe文件")
            return False
        
        # 复制说明文档
        readme_src = self.project_root / "使用说明.md"
        if readme_src.exists():
            readme_dst = self.release_dir / "使用说明.md"
            shutil.copy2(readme_src, readme_dst)
        
        print("✅ 发布包创建完成")
        return True
    
    def build_all(self):
        """完整构建流程"""
        print("🚀 开始构建发布版本...")
        print("=" * 50)
        
        try:
            # 1. 清理构建目录
            self.clean_build()
            
            # 2. 复制用户端文件
            self.copy_user_files()
            
            # 3. 安全性验证
            if not self.verify_no_admin_files():
                print("❌ 安全性验证失败，构建中止")
                return False
            
            if not self.check_config_security():
                print("❌ 配置安全性检查失败，构建中止")
                return False
            
            # 4. 构建exe
            if not self.build_exe():
                print("❌ exe构建失败，构建中止")
                return False
            
            # 5. 创建发布包
            if not self.create_release_package():
                print("❌ 发布包创建失败")
                return False
            
            print("=" * 50)
            print("🎉 构建完成！")
            print(f"📁 发布文件位置: {self.release_dir}")
            print("\n📋 发布包内容:")
            print("  - 批量重命名工具.exe")
            print("  - 使用说明.md")
            print("\n⚠️ 注意事项:")
            print("  - 管理员工具未包含在发布包中")
            print("  - 配置文件不含敏感信息")
            print("  - 请在发布前测试exe文件")
            
            return True
            
        except Exception as e:
            print(f"❌ 构建过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    builder = ReleaseBuilder()
    
    print("批量重命名工具 - 发布版本构建器")
    print("=" * 50)
    
    # 检查依赖
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
    except ImportError:
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return
    
    # 确认构建
    response = input("\n是否开始构建发布版本？(y/N): ")
    if response.lower() != 'y':
        print("构建已取消")
        return
    
    # 开始构建
    success = builder.build_all()
    
    if success:
        print("\n🎉 构建成功完成！")
    else:
        print("\n❌ 构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
