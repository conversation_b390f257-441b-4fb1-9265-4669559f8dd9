#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激活码管理工具 - 生成和管理激活码
"""

import random
import string
import hashlib
import requests
import json
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

class ActivationCodeManager:
    """激活码管理器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("激活码管理工具")

        # 加载管理员配置
        try:
            from admin_config import CLOUD_FUNCTION_URL, ADMIN_SECRET_KEY, DEFAULT_CODE_COUNT
            self.api_url = CLOUD_FUNCTION_URL
            self.default_admin_key = ADMIN_SECRET_KEY
            self.default_count = DEFAULT_CODE_COUNT
        except ImportError:
            self.api_url = "https://your-cloud-function-url"
            self.default_admin_key = ""
            self.default_count = 10

        self.admin_key = ""
        self.generated_codes = []

        # 创建验证界面
        self.create_login_interface()
    
    def create_login_interface(self):
        """创建验证界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 设置窗口标题和初始大小
        self.root.title("激活码管理工具 - 身份验证")

        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=40, pady=40)

        # 标题
        title_label = ttk.Label(main_frame, text="激活码管理系统",
                               font=("Microsoft YaHei", 18, "bold"))
        title_label.pack(pady=(0, 30))

        # 验证区域
        login_frame = ttk.LabelFrame(main_frame, text="管理员身份验证", padding="20")
        login_frame.pack(fill=tk.X, pady=20)

        # 密钥输入
        key_input_frame = ttk.Frame(login_frame)
        key_input_frame.pack(fill=tk.X, pady=10)

        ttk.Label(key_input_frame, text="管理员密钥:",
                 font=("Microsoft YaHei", 10)).pack(anchor=tk.W, pady=(0, 5))

        self.key_entry = ttk.Entry(key_input_frame, show="*", width=40,
                                  font=("Microsoft YaHei", 10))
        self.key_entry.pack(fill=tk.X, pady=(0, 15))

        # 验证按钮
        verify_btn = ttk.Button(key_input_frame, text="验证身份",
                               command=self.verify_admin_and_switch)
        verify_btn.pack(pady=10)

        # 绑定回车键
        self.key_entry.bind('<Return>', lambda e: self.verify_admin_and_switch())

        # 设置焦点
        self.key_entry.focus()

        # 自动调整窗口大小
        self.auto_resize_window()

    def auto_resize_window(self):
        """自动调整窗口大小"""
        self.root.update_idletasks()
        min_width = max(400, self.root.winfo_reqwidth() + 50)
        min_height = max(300, self.root.winfo_reqheight() + 50)

        # 设置窗口大小和最小尺寸
        self.root.geometry(f"{min_width}x{min_height}")
        self.root.minsize(min_width, min_height)

        # 居中显示
        self.center_window()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def verify_admin_and_switch(self):
        """验证管理员身份并切换界面"""
        self.admin_key = self.key_entry.get().strip()

        if not self.admin_key:
            messagebox.showerror("错误", "请输入管理员密钥")
            return

        # 这里可以添加密钥验证逻辑
        # 目前直接通过，实际使用时可以验证密钥是否正确

        messagebox.showinfo("验证成功", "身份验证通过，正在进入管理界面...")
        self.create_admin_interface()

    def create_admin_interface(self):
        """创建管理员界面"""
        # 清空窗口
        for widget in self.root.winfo_children():
            widget.destroy()

        # 设置窗口标题
        self.root.title("激活码管理工具 - 管理界面")

        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(title_frame, text="激活码管理系统",
                 font=("Microsoft YaHei", 16, "bold")).pack(side=tk.LEFT)

        ttk.Button(title_frame, text="退出登录",
                  command=self.logout).pack(side=tk.RIGHT)

        # 创建所有管理功能
        self.create_admin_functions(main_frame)

        # 自动调整窗口大小
        self.auto_resize_window()

    def logout(self):
        """退出登录，返回验证界面"""
        self.admin_key = ""
        self.generated_codes = []
        self.create_login_interface()

    def create_admin_functions(self, parent_frame):
        """创建管理功能界面"""
        # 生成激活码区域
        gen_frame = ttk.LabelFrame(parent_frame, text="生成激活码", padding="15")
        gen_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 输入和按钮区域
        input_frame = ttk.Frame(gen_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="生成数量:").pack(side=tk.LEFT)
        self.count_entry = ttk.Entry(input_frame, width=10)
        self.count_entry.pack(side=tk.LEFT, padx=(5, 15))
        self.count_entry.insert(0, str(self.default_count))

        ttk.Button(input_frame, text="生成激活码",
                  command=self.generate_codes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(input_frame, text="上传到云端",
                  command=self.upload_codes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(input_frame, text="保存到文件",
                  command=self.save_to_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(input_frame, text="清空",
                  command=self.clear_codes).pack(side=tk.LEFT)

        # 激活码显示区域
        display_frame = ttk.LabelFrame(parent_frame, text="生成的激活码", padding="15")
        display_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 文本框和滚动条
        text_frame = ttk.Frame(display_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.codes_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.codes_text.yview)
        self.codes_text.configure(yscrollcommand=scrollbar.set)
        
        self.codes_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 操作按钮
        button_frame = ttk.Frame(display_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="保存到文件",
                  command=self.save_to_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空",
                  command=self.clear_codes).pack(side=tk.LEFT, padx=5)


    
    def generate_codes(self):
        """生成激活码"""
        try:
            count = int(self.count_entry.get())
            if count <= 0 or count > 10000:
                messagebox.showerror("错误", "数量必须在1-10000之间")
                return
            
            # 生成激活码
            codes = self.generate_activation_codes(count)
            self.generated_codes = codes
            
            # 显示在文本框中
            self.codes_text.delete(1.0, tk.END)
            for i, code in enumerate(codes, 1):
                self.codes_text.insert(tk.END, f"{i:04d}: {code}\n")
            
            messagebox.showinfo("成功", f"成功生成 {count} 个激活码")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数量")
        except Exception as e:
            messagebox.showerror("错误", f"生成失败: {str(e)}")
    
    def generate_activation_codes(self, count):
        """生成激活码列表"""
        codes = []
        timestamp = datetime.now().strftime("%Y%m%d")
        
        for i in range(count):
            # 生成随机部分
            random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            
            # 添加时间戳和序号
            sequence = f"{timestamp}{i:04d}"
            
            # 生成校验码
            checksum = hashlib.md5(f"PREMIUM{random_part}{sequence}".encode()).hexdigest()[:2].upper()
            
            # 组合激活码
            code = f"PREMIUM-{random_part}-{sequence}-{checksum}"
            codes.append(code)
        
        return codes
    
    def upload_codes(self):
        """上传激活码到云端"""
        if not self.generated_codes:
            messagebox.showwarning("警告", "请先生成激活码")
            return
        
        try:
            data = {
                "admin_key": self.admin_key,
                "codes": self.generated_codes
            }
            
            response = requests.post(
                f"{self.api_url}/admin/add_codes",
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    messagebox.showinfo("成功", result.get("message", "上传成功"))
                else:
                    messagebox.showerror("失败", result.get("message", "上传失败"))
            else:
                messagebox.showerror("错误", f"网络错误: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("错误", f"上传失败: {str(e)}")
    
    def save_to_file(self):
        """保存激活码到文件"""
        if not self.generated_codes:
            messagebox.showwarning("警告", "没有激活码可保存")
            return
        
        filename = filedialog.asksaveasfilename(
            title="保存激活码",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"# 激活码备份 - {datetime.now()}\n")
                    f.write(f"# 总数: {len(self.generated_codes)}\n\n")
                    
                    for i, code in enumerate(self.generated_codes, 1):
                        f.write(f"{i:04d}: {code}\n")
                
                messagebox.showinfo("成功", f"激活码已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_codes(self):
        """清空激活码"""
        self.codes_text.delete(1.0, tk.END)
        self.generated_codes = []



    def run(self):
        """运行管理工具"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ActivationCodeManager()
    app.run()

if __name__ == "__main__":
    main()
